import { defineBackground } from 'wxt/utils/define-background';
import * as jsondiffpatch from 'jsondiffpatch';

// 类型定义
interface InterceptRule {
  id: string;
  name: string;
  description?: string;
  enabled: boolean;
  priority: number;
  createdAt: number;
  updatedAt: number;
  conditions: {
    urlPattern: string;
    urlMatchType: 'contains' | 'startsWith' | 'endsWith' | 'regex' | 'exact';
    methods?: string[];
    headers?: Record<string, string>;
  };
  transformation: {
    newUrl: string;
    paramMapping?: Record<string, string>;
    headerMapping?: Record<string, string>;
    preserveOriginalParams?: boolean;
    includeCookies?: boolean;
  };
  mode: 'redirect' | 'parallel';
  diffConfig?: {
    ignoreFields?: string[];
    caseSensitive?: boolean;
    arrayOrderSensitive?: boolean;
    numericTolerance?: number;
  };
}

interface DiffReport {
  id: string;
  ruleId: string;
  ruleName: string;
  timestamp: number;
  request: {
    url: string;
    method: string;
    headers: Record<string, string>;
    body?: any;
  };
  responses: {
    old: { status: number; body: any; responseTime: number; error?: string };
    new: { status: number; body: any; responseTime: number; error?: string };
  };
  diff: {
    delta: any;
    hasChanges: boolean;
    changeCount: number;
    severity: 'none' | 'minor' | 'major' | 'critical';
  };
  visualizations: {
    html: string;
    summary?: string;
  };
}

export default defineBackground(() => {
  console.log('🚀 增强版API迁移验证Service Worker启动');

  // 消息监听器
  browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('📨 收到消息:', request);

    if (request.type === 'ping') {
      console.log('🏓 响应ping');
      sendResponse({ success: true, message: 'pong' });
      return true;
    }

    // API迁移消息处理
    if (request.type === 'api-migration') {
      console.log('🎯 处理API迁移消息...');
      handleApiMigrationMessage(request, sender, sendResponse).catch(error => {
        console.error('处理API迁移消息异常:', error);
        sendResponse({ success: false, error: error.message });
      });
      return true;
    }

    return false;
  });

  // 安装事件
  browser.runtime.onInstalled.addListener(() => {
    console.log('✅ 增强版Service Worker已安装');
  });

  // 启动事件
  browser.runtime.onStartup.addListener(() => {
    console.log('🔄 增强版Service Worker已启动');
  });

  console.log('✅ 增强版Service Worker初始化完成');

  // 初始化API迁移功能
  initApiMigration().then(() => {
    console.log('✅ 增强版API迁移后台功能初始化完成');
  }).catch((error: any) => {
    console.error('❌ 增强版API迁移后台功能初始化失败:', error);
  });
});

// API迁移功能变量
let apiMigrationRules: InterceptRule[] = [];
let isIntercepting = false;
let activeRequests = new Map<string, any>(); // 跟踪活跃的请求
let processingRequests = new Set<string>(); // 跟踪正在处理的请求URL，防止死循环

// 域名过滤配置
const domainFilterConfig = {
  enabled: true, // 是否启用域名过滤
  mode: 'whitelist' as 'whitelist' | 'blacklist', // 白名单或黑名单模式
  domains: new Set<string>(), // 缓存的域名列表
  patterns: new Set<string>() // 缓存的URL模式
};

// 预定义的常见API域名（当没有规则时使用）
const commonApiDomains = [
  // 开发测试域名
  'localhost',
  '127.0.0.1',
  'assistantdesk-base-cc.suanshubang.cc',
];

// 初始化JSON差异对比器
const differ = jsondiffpatch.create({
  objectHash: function(obj: any) {
    return obj.id || obj._id || obj.key || JSON.stringify(obj);
  },
  arrays: {
    detectMove: true,
    includeValueOnMove: false
  }
});

// 初始化API迁移功能
async function initApiMigration() {
  console.log('🔧 增强版API迁移验证工具后台功能已初始化');
  console.log('📋 开始加载API迁移规则...');
  await loadApiMigrationRules();
  console.log('✅ API迁移规则加载完成');

  // 更新域名过滤器
  updateDomainFilter();
}

// 更新域名过滤器
function updateDomainFilter() {
  if (!domainFilterConfig.enabled) {
    console.log('🔍 域名过滤已禁用，将拦截所有请求');
    return;
  }

  // 清空现有的域名和模式
  domainFilterConfig.domains.clear();
  domainFilterConfig.patterns.clear();

  // 从规则中提取域名和模式
  for (const rule of apiMigrationRules) {
    if (!rule.enabled) continue;

    const domains = extractDomainsFromRule(rule);
    domains.forEach(domain => {
      domainFilterConfig.domains.add(domain);
      console.log(`🎯 添加域名过滤: ${domain}`);
    });
  }

  // 如果没有从规则中提取到域名，使用预定义的常见API域名
  if (domainFilterConfig.domains.size === 0) {
    console.log('📝 未从规则中提取到域名，使用预定义的常见API域名');
    commonApiDomains.forEach(domain => {
      domainFilterConfig.domains.add(domain);
      console.log(`🎯 添加预定义域名: ${domain}`);
    });
  }

  console.log(`✅ 域名过滤器已更新，共 ${domainFilterConfig.domains.size} 个域名`);
}

// 从规则中提取域名
function extractDomainsFromRule(rule: InterceptRule): string[] {
  const domains: string[] = [];
  const urlPattern = rule.conditions.urlPattern;

  try {
    // 处理不同的URL模式
    switch (rule.conditions.urlMatchType) {
      case 'exact':
        const exactDomain = extractDomainFromUrl(urlPattern);
        if (exactDomain) domains.push(exactDomain);
        break;

      case 'startsWith':
        if (urlPattern.startsWith('http://') || urlPattern.startsWith('https://')) {
          const domain = extractDomainFromUrl(urlPattern);
          if (domain) domains.push(domain);
        }
        break;

      case 'contains':
        // 尝试从包含的字符串中提取域名
        const containsDomain = extractDomainFromPattern(urlPattern);
        if (containsDomain) domains.push(containsDomain);
        break;

      case 'regex':
        // 从正则表达式中提取域名（简单处理）
        const regexDomains = extractDomainsFromRegex(urlPattern);
        domains.push(...regexDomains);
        break;

      case 'endsWith':
        // 对于endsWith，通常是路径匹配，不太适合域名提取
        break;
    }

    // 同时处理transformation.newUrl中的域名
    const newDomain = extractDomainFromUrl(rule.transformation.newUrl);
    if (newDomain && !domains.includes(newDomain)) {
      domains.push(newDomain);
    }
  } catch (error) {
    console.warn(`⚠️ 从规则 ${rule.name} 提取域名失败:`, error);
  }

  return domains;
}

// 从URL中提取域名
function extractDomainFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch {
    // 如果不是完整URL，尝试其他方法
    const match = url.match(/(?:https?:\/\/)?([^\/\s?#]+)/);
    return match ? match[1] : null;
  }
}

// 从模式中提取域名
function extractDomainFromPattern(pattern: string): string | null {
  // 查找类似域名的模式
  const domainMatch = pattern.match(/([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}/);
  return domainMatch ? domainMatch[0] : null;
}

// 从正则表达式中提取域名（简单实现）
function extractDomainsFromRegex(regex: string): string[] {
  const domains: string[] = [];

  // 查找正则中的域名模式
  const domainMatches = regex.match(/([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}/g);
  if (domainMatches) {
    domains.push(...domainMatches);
  }

  return domains;
}

// 检查URL是否应该被拦截
function shouldInterceptUrl(url: string): boolean {
  if (!domainFilterConfig.enabled) {
    return true; // 如果禁用过滤，拦截所有请求
  }

  if (domainFilterConfig.domains.size === 0) {
    return true; // 如果没有配置域名，拦截所有请求
  }

  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname;

    // 检查精确匹配
    if (domainFilterConfig.domains.has(hostname)) {
      return true;
    }

    // 检查子域名匹配
    for (const domain of domainFilterConfig.domains) {
      if (hostname.endsWith('.' + domain) || hostname === domain) {
        return true;
      }
    }

    return domainFilterConfig.mode === 'blacklist'; // 黑名单模式下，不在列表中的允许通过
  } catch {
    return true; // URL解析失败时，保守地拦截
  }
}

// 加载API迁移规则
async function loadApiMigrationRules() {
  try {
    const result = await browser.storage.local.get(['apiMigrationRules']);
    apiMigrationRules = result.apiMigrationRules || [];
    console.log('📋 已加载', apiMigrationRules.length, '条API迁移规则');
  } catch (error) {
    console.error('加载API迁移规则失败:', error);
    apiMigrationRules = [];
  }
}

// 保存API迁移规则
async function saveApiMigrationRules() {
  try {
    await browser.storage.local.set({ apiMigrationRules });
    console.log('💾 API迁移规则已保存');
  } catch (error) {
    console.error('保存API迁移规则失败:', error);
  }
}

// API迁移功能
async function handleApiMigrationMessage(request: any, _sender: any, sendResponse: any) {
  console.log('📨 background收到API迁移消息:', request);

  try {
    let response;

    switch (request.action) {
      case 'start-interceptor':
        await startApiInterceptor();
        response = { success: true };
        break;

      case 'stop-interceptor':
        await stopApiInterceptor();
        response = { success: true };
        break;

      case 'get-status':
        console.log('📊 返回拦截器状态:', isIntercepting);
        response = { success: true, isIntercepting };
        break;

      case 'update-rules':
        apiMigrationRules = request.rules;
        await saveApiMigrationRules();
        console.log('🔄 规则已动态更新:', apiMigrationRules.length, '条规则');

        // 更新域名过滤器
        updateDomainFilter();
        response = { success: true };
        break;

      case 'get-rules':
        console.log('📋 返回规则数据:', apiMigrationRules.length, '条规则');
        response = { success: true, rules: apiMigrationRules };
        break;

      case 'get-domain-filter':
        response = {
          success: true,
          config: {
            enabled: domainFilterConfig.enabled,
            mode: domainFilterConfig.mode,
            domains: Array.from(domainFilterConfig.domains)
          }
        };
        break;

      case 'update-domain-filter':
        if (request.config) {
          domainFilterConfig.enabled = request.config.enabled ?? domainFilterConfig.enabled;
          domainFilterConfig.mode = request.config.mode ?? domainFilterConfig.mode;

          // 如果提供了自定义域名列表，使用它
          if (request.config.customDomains) {
            domainFilterConfig.domains.clear();
            request.config.customDomains.forEach((domain: string) => {
              domainFilterConfig.domains.add(domain);
            });
          } else {
            // 否则从规则中重新提取
            updateDomainFilter();
          }

          console.log('🔧 域名过滤配置已更新:', domainFilterConfig);
        }
        response = { success: true };
        break;

      default:
        response = { success: false, error: '未知动作' };
    }

    sendResponse(response);
  } catch (error: any) {
    console.error('处理API迁移消息失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 增强版网络拦截功能
async function startApiInterceptor() {
  if (isIntercepting) {
    console.log('增强版API迁移拦截器已在运行');
    return;
  }

  try {
    console.log('🔄 启动增强版API迁移拦截器');

    // 添加请求拦截器 - 使用webRequest API捕获所有页面API请求
    browser.webRequest.onBeforeRequest.addListener(
      handleBeforeRequest,
      { urls: ['<all_urls>'] }
    );

    // 添加响应头拦截器，用于获取响应信息
    browser.webRequest.onHeadersReceived.addListener(
      handleHeadersReceived,
      { urls: ['<all_urls>'] }
    );

    isIntercepting = true;
    console.log('✅ 增强版API迁移拦截器已启动');
  } catch (error) {
    console.error('启动增强版API迁移拦截器失败:', error);
    throw error;
  }
}

async function stopApiInterceptor() {
  if (!isIntercepting) {
    console.log('增强版API迁移拦截器未在运行');
    return;
  }

  try {
    console.log('⏹️ 停止增强版API迁移拦截器');

    browser.webRequest.onBeforeRequest.removeListener(handleBeforeRequest);
    browser.webRequest.onHeadersReceived.removeListener(handleHeadersReceived);

    // 清理活跃请求和处理中的请求
    activeRequests.clear();
    processingRequests.clear();

    isIntercepting = false;
    console.log('✅ 增强版API迁移拦截器已停止');
  } catch (error) {
    console.error('停止增强版API迁移拦截器失败:', error);
    throw error;
  }
}

// 处理响应头拦截
function handleHeadersReceived(details: any): undefined {
  // 记录响应信息，用于后续的并行对比
  const requestId = details.requestId;
  if (activeRequests.has(requestId)) {
    const requestInfo = activeRequests.get(requestId);
    requestInfo.responseHeaders = details.responseHeaders;
    requestInfo.statusCode = details.statusCode;
    activeRequests.set(requestId, requestInfo);
  }
  return undefined;
}

// 增强版请求拦截处理
function handleBeforeRequest(details: any): undefined {
  // 只处理主要的API请求
  if (details.type !== 'main_frame' && details.type !== 'xmlhttprequest' && details.type !== 'fetch') {
    return undefined;
  }

  // 检查是否为内部并行对比请求，避免死循环
  try {
    const url = new URL(details.url);
    if (url.searchParams.has('__api_migration_internal__')) {
      return undefined;
    }
  } catch (error) {
    // URL解析失败，继续处理
  }

  // 域名过滤检查
  if (!shouldInterceptUrl(details.url)) {
    return undefined;
  }

  console.log('🔍 处理API请求:', details.url);

  // 检查是否已经在处理相同的URL，防止死循环
  if (processingRequests.has(details.url)) {
    console.log('⚠️ 请求已在处理中，跳过以防止死循环:', details.url);
    return undefined;
  }

  const rule = findMatchingRule(details.url, details.method);
  if (!rule) {
    return undefined;
  }

  console.log('✅ 找到匹配规则:', rule.name);

  // 标记请求正在处理
  processingRequests.add(details.url);

  // 记录请求信息
  activeRequests.set(details.requestId, {
    url: details.url,
    method: details.method,
    rule: rule,
    timestamp: Date.now(),
    requestBody: details.requestBody
  });

  try {
    // 检查规则模式
    if (rule.mode === 'redirect') {
      // 重定向模式（在Manifest V3中只能记录）
      const newUrl = transformUrl(details.url, rule);
      console.log('� 重定向模式记录:', { original: details.url, new: newUrl });

      // 创建重定向报告
      createRedirectReport(details, rule, newUrl).finally(() => {
        // 处理完成后清理标记
        processingRequests.delete(details.url);
      });
    } else if (rule.mode === 'parallel') {
      // 并行对比模式 - 真正的并行请求
      console.log('🔄 启动增强版并行对比模式');
      performEnhancedParallelComparison(details, rule).catch((error: any) => {
        console.error('增强版并行对比失败:', error);
      }).finally(() => {
        // 处理完成后清理标记
        processingRequests.delete(details.url);
      });
    }

  } catch (error) {
    console.error('处理API请求失败:', error);
    // 发生错误时也要清理标记
    processingRequests.delete(details.url);
  }

  return undefined;
}

// 查找匹配的规则
function findMatchingRule(url: string, method: string) {
  const enabledRules = apiMigrationRules.filter(rule => rule.enabled)
    .sort((a, b) => b.priority - a.priority);

  for (const rule of enabledRules) {
    if (isRuleMatching(rule, url, method)) {
      return rule;
    }
  }

  return null;
}

// 检查规则是否匹配
function isRuleMatching(rule: any, url: string, method: string) {
  const { conditions } = rule;

  // 检查URL匹配
  if (!isUrlMatching(conditions.urlPattern, conditions.urlMatchType, url)) {
    return false;
  }

  // 检查方法匹配
  if (conditions.methods && conditions.methods.length > 0 &&
      !conditions.methods.includes(method.toUpperCase())) {
    return false;
  }

  return true;
}

// URL匹配检查
function isUrlMatching(pattern: string, matchType: string, url: string) {
  switch (matchType) {
    case 'exact':
      return url === pattern;
    case 'contains':
      return url.includes(pattern);
    case 'startsWith':
      return url.startsWith(pattern);
    case 'endsWith':
      return url.endsWith(pattern);
    case 'regex':
      try {
        return new RegExp(pattern).test(url);
      } catch {
        return false;
      }
    default:
      return url.includes(pattern);
  }
}

// URL转换
function transformUrl(originalUrl: string, rule: any) {
  const { transformation } = rule;
  let newUrl = transformation.newUrl;

  // 处理参数映射
  if (transformation.paramMapping) {
    try {
      const originalUrlObj = new URL(originalUrl);
      const newUrlObj = new URL(newUrl);

      // 映射参数
      for (const [oldParam, newParam] of Object.entries(transformation.paramMapping)) {
        const value = originalUrlObj.searchParams.get(oldParam);
        if (value && newParam) {
          newUrlObj.searchParams.set(newParam as string, value);
        }
      }

      // 保留原始参数
      if (transformation.preserveOriginalParams) {
        originalUrlObj.searchParams.forEach((value, key) => {
          if (!transformation.paramMapping[key] && !newUrlObj.searchParams.has(key)) {
            newUrlObj.searchParams.set(key, value);
          }
        });
      }

      newUrl = newUrlObj.toString();
    } catch (error) {
      console.warn('URL转换失败:', error);
    }
  }

  return newUrl;
}


// 创建重定向报告
async function createRedirectReport(details: any, rule: InterceptRule, newUrl: string) {
  try {
    const report: DiffReport = {
      id: 'redirect_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
      ruleId: rule.id,
      ruleName: rule.name,
      timestamp: Date.now(),
      request: {
        url: details.url,
        method: details.method,
        headers: {},
        body: details.requestBody
      },
      responses: {
        old: { status: 0, body: null, responseTime: 0, error: '重定向模式，无原始响应' },
        new: { status: 0, body: null, responseTime: 0, error: '重定向模式，无新响应' }
      },
      diff: {
        delta: null,
        hasChanges: true,
        changeCount: 1,
        severity: 'minor'
      },
      visualizations: {
        html: `<div class="redirect-report">
          <h4>重定向记录</h4>
          <p><strong>原始URL:</strong> ${details.url}</p>
          <p><strong>目标URL:</strong> ${newUrl}</p>
          <p><strong>规则:</strong> ${rule.name}</p>
        </div>`,
        summary: `URL重定向: ${details.url} → ${newUrl}`
      }
    };

    // 保存报告
    await saveReportToStorage(report);
    console.log('✅ 重定向报告已创建:', report.id);
  } catch (error) {
    console.error('❌ 创建重定向报告失败:', error);
  }
}

// 增强版并行对比功能 - 真正的并行请求
async function performEnhancedParallelComparison(details: any, rule: InterceptRule) {
  try {
    console.log('🔄 开始增强版并行对比:', details.url);

    const originalUrl = details.url;
    const newUrl = transformUrl(originalUrl, rule);

    console.log('🎯 对比URL:', { original: originalUrl, new: newUrl });

    // 准备请求选项
    const requestOptions = {
      method: details.method,
      headers: transformHeaders(details.requestHeaders || {}, rule),
      credentials: rule.transformation.includeCookies ? 'include' : 'omit'
    } as RequestInit;

    // 如果有请求体，添加到选项中
    if (details.requestBody && details.method !== 'GET') {
      requestOptions.body = details.requestBody;
    }

    console.log('🚀 开始并行请求...');
    const startTime = Date.now();

    // 使用Promise.allSettled同时调用新老接口
    const [oldResult, newResult] = await Promise.allSettled([
      performRequest(originalUrl, requestOptions, 'original'),
      performRequest(newUrl, requestOptions, 'new')
    ]);

    const endTime = Date.now();
    console.log(`⏱️ 并行请求完成，总耗时: ${endTime - startTime}ms`);

    // 处理请求结果
    const oldResponse = oldResult.status === 'fulfilled' ? oldResult.value : {
      status: 0,
      body: null,
      responseTime: 0,
      error: oldResult.reason?.message || '请求失败'
    };

    const newResponse = newResult.status === 'fulfilled' ? newResult.value : {
      status: 0,
      body: null,
      responseTime: 0,
      error: newResult.reason?.message || '请求失败'
    };

    // 执行JSON差异检测
    console.log('🔍 开始JSON差异检测...');
    const diffResult = performJsonDiff(oldResponse.body, newResponse.body, rule.diffConfig);

    // 创建详细报告
    const report: DiffReport = {
      id: 'parallel_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
      ruleId: rule.id,
      ruleName: rule.name,
      timestamp: Date.now(),
      request: {
        url: originalUrl,
        method: details.method,
        headers: details.requestHeaders || {},
        body: details.requestBody
      },
      responses: {
        old: oldResponse,
        new: newResponse
      },
      diff: diffResult,
      visualizations: {
        html: generateDiffVisualization(diffResult, oldResponse, newResponse),
        summary: generateDiffSummary(diffResult, oldResponse, newResponse)
      }
    };

    // 保存报告
    await saveReportToStorage(report);
    console.log('✅ 增强版并行对比报告已创建:', report.id);

  } catch (error) {
    console.error('❌ 增强版并行对比失败:', error);
  }
}

// 执行单个请求
async function performRequest(url: string, options: RequestInit, type: string) {
  const startTime = Date.now();

  try {
    // 为内部请求添加标识参数，避免被拦截器再次处理
    const urlObj = new URL(url);
    urlObj.searchParams.set('__api_migration_internal__', 'true');
    const internalUrl = urlObj.toString();

    console.log(`📡 发起${type}请求:`, internalUrl);

    const response = await fetch(internalUrl, options);
    const responseTime = Date.now() - startTime;

    let body;
    const contentType = response.headers.get('content-type');

    if (contentType && contentType.includes('application/json')) {
      body = await response.json();
    } else {
      body = await response.text();
    }

    console.log(`✅ ${type}请求完成:`, response.status, `(${responseTime}ms)`);

    return {
      status: response.status,
      body: body,
      responseTime: responseTime
    };
  } catch (error: any) {
    const responseTime = Date.now() - startTime;
    console.error(`❌ ${type}请求失败:`, error.message);

    return {
      status: 0,
      body: null,
      responseTime: responseTime,
      error: error.message
    };
  }
}

// 保存报告到存储
async function saveReportToStorage(report: DiffReport) {
  try {
    // 获取现有的报告列表
    const result = await browser.storage.local.get('apiMigrationReports');
    const reports = result.apiMigrationReports || [];

    // 添加新报告到列表开头
    reports.unshift(report);

    // 限制报告数量（保留最新的100个）
    if (reports.length > 100) {
      reports.length = 100;
    }

    // 保存到storage
    await browser.storage.local.set({ apiMigrationReports: reports });
    console.log('💾 报告已保存到storage:', report.id);
  } catch (error) {
    console.error('❌ 保存报告到storage失败:', error);
  }
}

// 转换请求头
function transformHeaders(originalHeaders: Record<string, string>, rule: InterceptRule): Record<string, string> {
  const headers = { ...originalHeaders };

  // 应用请求头映射
  if (rule.transformation.headerMapping) {
    for (const [oldHeader, newHeader] of Object.entries(rule.transformation.headerMapping)) {
      if (headers[oldHeader]) {
        headers[newHeader] = headers[oldHeader];
        delete headers[oldHeader];
      }
    }
  }

  return headers;
}

// 执行JSON差异检测
function performJsonDiff(oldData: any, newData: any, diffConfig?: InterceptRule['diffConfig']) {
  try {
    console.log('🔍 执行JSON差异检测...');

    // 预处理数据（忽略指定字段）
    const processedOldData = preprocessData(oldData, diffConfig);
    const processedNewData = preprocessData(newData, diffConfig);

    // 使用jsondiffpatch进行差异检测
    const delta = differ.diff(processedOldData, processedNewData);

    // 分析差异
    const hasChanges = !!delta;
    const changeCount = hasChanges ? countChanges(delta) : 0;
    const severity = determineSeverity(changeCount, delta);

    console.log(`📊 差异检测完成: ${changeCount} 处变更, 严重程度: ${severity}`);

    return {
      delta: delta,
      hasChanges: hasChanges,
      changeCount: changeCount,
      severity: severity
    };
  } catch (error) {
    console.error('❌ JSON差异检测失败:', error);
    return {
      delta: null,
      hasChanges: false,
      changeCount: 0,
      severity: 'none' as const
    };
  }
}

// 预处理数据（移除忽略字段）
function preprocessData(data: any, diffConfig?: InterceptRule['diffConfig']): any {
  if (!data || !diffConfig?.ignoreFields) {
    return data;
  }

  const processed = JSON.parse(JSON.stringify(data)); // 深拷贝

  // 移除忽略的字段
  for (const fieldPath of diffConfig.ignoreFields) {
    removeFieldByPath(processed, fieldPath);
  }

  return processed;
}

// 根据路径移除字段
function removeFieldByPath(obj: any, path: string) {
  const parts = path.split('.');
  let current = obj;

  for (let i = 0; i < parts.length - 1; i++) {
    if (current && typeof current === 'object' && parts[i] in current) {
      current = current[parts[i]];
    } else {
      return; // 路径不存在
    }
  }

  if (current && typeof current === 'object') {
    delete current[parts[parts.length - 1]];
  }
}

// 计算变更数量
function countChanges(delta: any): number {
  if (!delta) return 0;

  let count = 0;

  function traverse(obj: any) {
    for (const key in obj) {
      if (key.startsWith('_')) continue; // 跳过jsondiffpatch的内部字段

      const value = obj[key];
      if (Array.isArray(value)) {
        count++;
      } else if (typeof value === 'object' && value !== null) {
        traverse(value);
      } else {
        count++;
      }
    }
  }

  traverse(delta);
  return count;
}

// 确定严重程度
function determineSeverity(changeCount: number, _delta: any): 'none' | 'minor' | 'major' | 'critical' {
  if (changeCount === 0) return 'none';
  if (changeCount <= 3) return 'minor';
  if (changeCount <= 10) return 'major';
  return 'critical';
}

// 生成差异可视化HTML
function generateDiffVisualization(diffResult: any, oldResponse: any, newResponse: any): string {
  const { delta, changeCount, severity } = diffResult;

  let html = `
    <div class="diff-visualization">
      <div class="diff-header">
        <h4>API响应对比结果</h4>
        <div class="diff-stats">
          <span class="change-count">变更数量: ${changeCount}</span>
          <span class="severity severity-${severity}">严重程度: ${severity}</span>
        </div>
      </div>
  `;

  if (changeCount === 0) {
    html += '<div class="no-changes">✅ 响应完全一致，无差异</div>';
  } else {
    html += `
      <div class="diff-content">
        <div class="response-section">
          <h5>原始响应 (状态: ${oldResponse.status})</h5>
          <pre class="response-body">${JSON.stringify(oldResponse.body, null, 2)}</pre>
        </div>
        <div class="response-section">
          <h5>新响应 (状态: ${newResponse.status})</h5>
          <pre class="response-body">${JSON.stringify(newResponse.body, null, 2)}</pre>
        </div>
      `;

    if (delta) {
      html += `
        <div class="delta-section">
          <h5>差异详情</h5>
          <pre class="delta-content">${JSON.stringify(delta, null, 2)}</pre>
        </div>
      `;
    }

    html += '</div>';
  }

  html += '</div>';
  return html;
}

// 生成差异摘要
function generateDiffSummary(diffResult: any, oldResponse: any, newResponse: any): string {
  const { changeCount, severity } = diffResult;

  if (changeCount === 0) {
    return '响应完全一致';
  }

  const statusDiff = oldResponse.status !== newResponse.status ?
    `状态码变化: ${oldResponse.status} → ${newResponse.status}` : '';

  const timeDiff = Math.abs(oldResponse.responseTime - newResponse.responseTime);
  const timeDiffText = timeDiff > 100 ? `, 响应时间差异: ${timeDiff}ms` : '';

  return `发现 ${changeCount} 处差异 (${severity})${statusDiff ? ', ' + statusDiff : ''}${timeDiffText}`;
}

