import { defineBackground } from 'wxt/utils/define-background';
import * as jsondiffpatch from 'jsondiffpatch';

// 类型定义
interface InterceptRule {
  id: string;
  name: string;
  description?: string;
  enabled: boolean;
  priority: number;
  createdAt: number;
  updatedAt: number;
  conditions: {
    urlPattern: string;
    urlMatchType: 'contains' | 'startsWith' | 'endsWith' | 'regex' | 'exact';
    methods?: string[];
  };
  transformation: {
    newUrl: string;
    paramMapping?: Record<string, string>;
    preserveOriginalParams?: boolean;
    includeCookies?: boolean;
  };
  diffConfig?: {
    ignoreFields?: string[];
  };
}

interface DiffReport {
  id: string;
  ruleId: string;
  ruleName: string;
  timestamp: number;
  request: {
    url: string;
    method: string;
    headers: Record<string, string>;
    body?: any;
  };
  responses: {
    old: { status: number; body: any; responseTime: number; error?: string };
    new: { status: number; body: any; responseTime: number; error?: string };
  };
  diff: {
    delta: any;
    hasChanges: boolean;
    changeCount: number;
    severity: 'none' | 'minor' | 'major' | 'critical';
  };
  visualizations: {
    html: string;
    summary?: string;
  };
}

export default defineBackground(() => {
  console.log('🚀 API迁移验证Service Worker启动');

  // 消息监听器
  browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('📨 收到消息:', request);

    if (request.type === 'ping') {
      console.log('🏓 响应ping');
      sendResponse({ success: true, message: 'pong' });
      return true;
    }

    // API迁移消息处理
    if (request.type === 'api-migration') {
      console.log('🎯 处理API迁移消息...');
      handleApiMigrationMessage(request, sender, sendResponse).catch(error => {
        console.error('处理API迁移消息异常:', error);
        sendResponse({ success: false, error: error.message });
      });
      return true;
    }

    return false;
  });

  // 安装事件
  browser.runtime.onInstalled.addListener(() => {
    console.log('✅ Service Worker已安装');
  });

  // 启动事件
  browser.runtime.onStartup.addListener(() => {
    console.log('🔄 Service Worker已启动');
  });

  console.log('✅ Service Worker初始化完成');

  // 初始化API迁移功能
  initApiMigration().then(() => {
    console.log('✅ API迁移后台功能初始化完成');
  }).catch((error: any) => {
    console.error('❌ API迁移后台功能初始化失败:', error);
  });
});

// API迁移功能变量
let apiMigrationRules: InterceptRule[] = [];
let isIntercepting = false;
let activeRequests = new Map<string, any>(); // 跟踪活跃的请求
let processingRequests = new Set<string>(); // 跟踪正在处理的请求URL，防止死循环

// 域名过滤配置
const domainFilterConfig = {
  enabled: true, // 是否启用域名过滤
  mode: 'whitelist' as 'whitelist' | 'blacklist', // 白名单或黑名单模式
  domains: new Set<string>(), // 缓存的域名列表
  patterns: new Set<string>() // 缓存的URL模式
};

// 预定义的常见API域名（当没有规则时使用）
const commonApiDomains = [
  // 开发测试域名
  'localhost',
  '127.0.0.1',
  'assistantdesk-base-cc.suanshubang.cc',
];

// 初始化JSON差异对比器
const differ = jsondiffpatch.create({
  objectHash: function(obj: any) {
    return obj.id || obj._id || obj.key || JSON.stringify(obj);
  },
  arrays: {
    detectMove: true,
    includeValueOnMove: false
  }
});

// 初始化API迁移功能
async function initApiMigration() {
  console.log('🔧 API迁移验证工具后台功能已初始化');
  console.log('📋 开始加载API迁移规则...');
  await loadApiMigrationRules();
  console.log('✅ API迁移规则加载完成');

  // 更新域名过滤器
  updateDomainFilter();
}

// 更新域名过滤器
function updateDomainFilter() {
  if (!domainFilterConfig.enabled) {
    console.log('🔍 域名过滤已禁用，将拦截所有请求');
    return;
  }

  // 清空现有的域名和模式
  domainFilterConfig.domains.clear();
  domainFilterConfig.patterns.clear();

  // 从规则中提取域名和模式
  for (const rule of apiMigrationRules) {
    if (!rule.enabled) continue;

    const domains = extractDomainsFromRule(rule);
    domains.forEach(domain => {
      domainFilterConfig.domains.add(domain);
      console.log(`🎯 添加域名过滤: ${domain}`);
    });
  }

  // 如果没有从规则中提取到域名，使用预定义的常见API域名
  if (domainFilterConfig.domains.size === 0) {
    console.log('📝 未从规则中提取到域名，使用预定义的常见API域名');
    commonApiDomains.forEach(domain => {
      domainFilterConfig.domains.add(domain);
      console.log(`🎯 添加预定义域名: ${domain}`);
    });
  }

  console.log(`✅ 域名过滤器已更新，共 ${domainFilterConfig.domains.size} 个域名`);
}

// 从规则中提取域名
function extractDomainsFromRule(rule: InterceptRule): string[] {
  const domains: string[] = [];
  const urlPattern = rule.conditions.urlPattern;

  try {
    // 处理不同的URL模式
    switch (rule.conditions.urlMatchType) {
      case 'exact':
        const exactDomain = extractDomainFromUrl(urlPattern);
        if (exactDomain) domains.push(exactDomain);
        break;

      case 'startsWith':
        if (urlPattern.startsWith('http://') || urlPattern.startsWith('https://')) {
          const domain = extractDomainFromUrl(urlPattern);
          if (domain) domains.push(domain);
        }
        break;

      case 'contains':
        // 尝试从包含的字符串中提取域名
        const containsDomain = extractDomainFromPattern(urlPattern);
        if (containsDomain) domains.push(containsDomain);
        break;

      case 'regex':
        // 从正则表达式中提取域名（简单处理）
        const regexDomains = extractDomainsFromRegex(urlPattern);
        domains.push(...regexDomains);
        break;

      case 'endsWith':
        // 对于endsWith，通常是路径匹配，不太适合域名提取
        break;
    }

    // 同时处理transformation.newUrl中的域名
    const newDomain = extractDomainFromUrl(rule.transformation.newUrl);
    if (newDomain && !domains.includes(newDomain)) {
      domains.push(newDomain);
    }
  } catch (error) {
    console.warn(`⚠️ 从规则 ${rule.name} 提取域名失败:`, error);
  }

  return domains;
}

// 从URL中提取域名
function extractDomainFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch {
    // 如果不是完整URL，尝试其他方法
    const match = url.match(/(?:https?:\/\/)?([^\/\s?#]+)/);
    return match ? match[1] : null;
  }
}

// 从模式中提取域名
function extractDomainFromPattern(pattern: string): string | null {
  // 查找类似域名的模式
  const domainMatch = pattern.match(/([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}/);
  return domainMatch ? domainMatch[0] : null;
}

// 从正则表达式中提取域名（简单实现）
function extractDomainsFromRegex(regex: string): string[] {
  const domains: string[] = [];

  // 查找正则中的域名模式
  const domainMatches = regex.match(/([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}/g);
  if (domainMatches) {
    domains.push(...domainMatches);
  }

  return domains;
}

// 检查URL是否应该被拦截
function shouldInterceptUrl(url: string): boolean {
  if (!domainFilterConfig.enabled) {
    return true; // 如果禁用过滤，拦截所有请求
  }

  if (domainFilterConfig.domains.size === 0) {
    return true; // 如果没有配置域名，拦截所有请求
  }

  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname;

    // 检查精确匹配
    if (domainFilterConfig.domains.has(hostname)) {
      return true;
    }

    // 检查子域名匹配
    for (const domain of domainFilterConfig.domains) {
      if (hostname.endsWith('.' + domain) || hostname === domain) {
        return true;
      }
    }

    return domainFilterConfig.mode === 'blacklist'; // 黑名单模式下，不在列表中的允许通过
  } catch {
    return true; // URL解析失败时，保守地拦截
  }
}

// 加载API迁移规则
async function loadApiMigrationRules() {
  try {
    const result = await browser.storage.local.get(['apiMigrationRules']);
    apiMigrationRules = result.apiMigrationRules || [];
    console.log('📋 已加载', apiMigrationRules.length, '条API迁移规则');
  } catch (error) {
    console.error('加载API迁移规则失败:', error);
    apiMigrationRules = [];
  }
}

// 保存API迁移规则
async function saveApiMigrationRules() {
  try {
    await browser.storage.local.set({ apiMigrationRules });
    console.log('💾 API迁移规则已保存');
  } catch (error) {
    console.error('保存API迁移规则失败:', error);
  }
}

// 处理API迁移消息
async function handleApiMigrationMessage(request: any, sender: any, sendResponse: any) {
  const { action, data } = request;

  switch (action) {
    case 'getRules':
      sendResponse({ success: true, data: apiMigrationRules });
      break;

    case 'addRule':
      const newRule: InterceptRule = {
        ...data,
        id: 'rule_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
        createdAt: Date.now(),
        updatedAt: Date.now()
      };
      apiMigrationRules.push(newRule);
      await saveApiMigrationRules();
      updateDomainFilter();
      sendResponse({ success: true, data: newRule });
      break;

    case 'updateRule':
      const ruleIndex = apiMigrationRules.findIndex(r => r.id === data.id);
      if (ruleIndex !== -1) {
        apiMigrationRules[ruleIndex] = { ...data, updatedAt: Date.now() };
        await saveApiMigrationRules();
        updateDomainFilter();
        sendResponse({ success: true, data: apiMigrationRules[ruleIndex] });
      } else {
        sendResponse({ success: false, error: '规则不存在' });
      }
      break;

    case 'deleteRule':
      const deleteIndex = apiMigrationRules.findIndex(r => r.id === data.id);
      if (deleteIndex !== -1) {
        apiMigrationRules.splice(deleteIndex, 1);
        await saveApiMigrationRules();
        updateDomainFilter();
        sendResponse({ success: true });
      } else {
        sendResponse({ success: false, error: '规则不存在' });
      }
      break;

    case 'startIntercepting':
      await startIntercepting();
      sendResponse({ success: true });
      break;

    case 'stopIntercepting':
      await stopIntercepting();
      sendResponse({ success: true });
      break;

    case 'getReports':
      const reports = await getReports();
      sendResponse({ success: true, data: reports });
      break;

    case 'clearReports':
      await clearReports();
      sendResponse({ success: true });
      break;

    default:
      sendResponse({ success: false, error: '未知操作' });
  }
}

// 启动拦截
async function startIntercepting() {
  if (isIntercepting) {
    console.log('⚠️ 拦截已经在运行中');
    return;
  }

  try {
    // 注册webRequest监听器
    if (browser.webRequest && browser.webRequest.onBeforeRequest) {
      browser.webRequest.onBeforeRequest.addListener(
        handleBeforeRequest,
        { urls: ['<all_urls>'] },
        ['requestBody']
      );
      console.log('✅ webRequest.onBeforeRequest 监听器已注册');
    }

    isIntercepting = true;
    console.log('🚀 API拦截已启动');
  } catch (error) {
    console.error('❌ 启动API拦截失败:', error);
    throw error;
  }
}

// 停止拦截
async function stopIntercepting() {
  if (!isIntercepting) {
    console.log('⚠️ 拦截未在运行');
    return;
  }

  try {
    // 移除webRequest监听器
    if (browser.webRequest && browser.webRequest.onBeforeRequest) {
      browser.webRequest.onBeforeRequest.removeListener(handleBeforeRequest);
      console.log('✅ webRequest.onBeforeRequest 监听器已移除');
    }

    isIntercepting = false;
    console.log('🛑 API拦截已停止');
  } catch (error) {
    console.error('❌ 停止API拦截失败:', error);
    throw error;
  }
}

// 增强版请求拦截处理
function handleBeforeRequest(details: any): undefined {
  // 只处理主要的API请求
  if (details.type !== 'main_frame' && details.type !== 'xmlhttprequest' && details.type !== 'fetch') {
    return undefined;
  }

  // 检查是否为内部并行对比请求，避免死循环
  try {
    const url = new URL(details.url);
    if (url.searchParams.has('__api_migration_internal__')) {
      return undefined;
    }
  } catch (error) {
    // URL解析失败，继续处理
  }

  // 域名过滤检查
  if (!shouldInterceptUrl(details.url)) {
    return undefined;
  }

  console.log('🔍 处理API请求:', details.url);

  // 检查是否已经在处理相同的URL，防止死循环
  if (processingRequests.has(details.url)) {
    console.log('⚠️ 请求已在处理中，跳过以防止死循环:', details.url);
    return undefined;
  }

  const rule = findMatchingRule(details.url, details.method);
  if (!rule) {
    return undefined;
  }

  console.log('✅ 找到匹配规则:', rule.name);

  // 标记请求正在处理
  processingRequests.add(details.url);

  // 记录请求信息
  activeRequests.set(details.requestId, {
    url: details.url,
    method: details.method,
    rule: rule,
    timestamp: Date.now(),
    requestBody: details.requestBody
  });

  try {
    // 并行对比模式 - 真正的并行请求
    console.log('🔄 启动并行对比模式');
    performEnhancedParallelComparison(details, rule).catch((error: any) => {
      console.error('并行对比失败:', error);
    }).finally(() => {
      // 处理完成后清理标记
      processingRequests.delete(details.url);
    });

  } catch (error) {
    console.error('处理API请求失败:', error);
    // 发生错误时也要清理标记
    processingRequests.delete(details.url);
  }

  return undefined;
}

// 查找匹配的规则
function findMatchingRule(url: string, method: string) {
  const enabledRules = apiMigrationRules.filter(rule => rule.enabled)
    .sort((a, b) => b.priority - a.priority);

  for (const rule of enabledRules) {
    if (isRuleMatching(rule, url, method)) {
      return rule;
    }
  }
  return null;
}

// 检查规则是否匹配
function isRuleMatching(rule: InterceptRule, url: string, method: string): boolean {
  // 检查HTTP方法
  if (rule.conditions.methods && rule.conditions.methods.length > 0) {
    if (!rule.conditions.methods.includes(method.toUpperCase())) {
      return false;
    }
  }

  // 检查URL模式
  const pattern = rule.conditions.urlPattern;
  const matchType = rule.conditions.urlMatchType;

  switch (matchType) {
    case 'exact':
      return url === pattern;
    case 'contains':
      return url.includes(pattern);
    case 'startsWith':
      return url.startsWith(pattern);
    case 'endsWith':
      return url.endsWith(pattern);
    case 'regex':
      try {
        const regex = new RegExp(pattern);
        return regex.test(url);
      } catch (error) {
        console.warn('正则表达式无效:', pattern, error);
        return false;
      }
    default:
      return false;
  }
}

// 获取报告
async function getReports(): Promise<DiffReport[]> {
  try {
    const result = await browser.storage.local.get(['apiMigrationReports']);
    return result.apiMigrationReports || [];
  } catch (error) {
    console.error('获取报告失败:', error);
    return [];
  }
}

// 清除报告
async function clearReports() {
  try {
    await browser.storage.local.remove(['apiMigrationReports']);
    console.log('✅ 报告已清除');
  } catch (error) {
    console.error('清除报告失败:', error);
  }
}

// 保存报告到存储
async function saveReportToStorage(report: DiffReport) {
  try {
    const reports = await getReports();
    reports.push(report);

    // 限制报告数量，保留最新的100个
    if (reports.length > 100) {
      reports.splice(0, reports.length - 100);
    }

    await browser.storage.local.set({ apiMigrationReports: reports });
    console.log('✅ 报告已保存:', report.id);
  } catch (error) {
    console.error('保存报告失败:', error);
  }
}

// 增强版并行对比功能 - 真正的并行请求
async function performEnhancedParallelComparison(details: any, rule: InterceptRule) {
  const startTime = Date.now();
  console.log('🔄 开始并行对比:', details.url);

  try {
    // 构建新URL
    const newUrl = transformUrl(details.url, rule);
    console.log('🎯 目标URL:', newUrl);

    // 准备请求头
    const headers = transformHeaders(details.requestHeaders || {}, rule);

    // 准备请求体
    let requestBody = details.requestBody;
    if (requestBody && requestBody.raw) {
      // 处理原始请求体
      const decoder = new TextDecoder();
      requestBody = decoder.decode(requestBody.raw[0].bytes);
    }

    // 并行发送两个请求
    const [oldResponse, newResponse] = await Promise.allSettled([
      makeRequest(details.url, details.method, headers, requestBody, rule),
      makeRequest(newUrl, details.method, headers, requestBody, rule)
    ]);

    // 处理响应结果
    const oldResult = oldResponse.status === 'fulfilled' ? oldResponse.value : {
      status: 0,
      body: null,
      responseTime: 0,
      error: oldResponse.reason?.message || '请求失败'
    };

    const newResult = newResponse.status === 'fulfilled' ? newResponse.value : {
      status: 0,
      body: null,
      responseTime: 0,
      error: newResponse.reason?.message || '请求失败'
    };

    // 生成差异报告
    const diffResult = generateDiff(oldResult.body, newResult.body, rule);

    // 创建完整报告
    const report: DiffReport = {
      id: 'parallel_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
      ruleId: rule.id,
      ruleName: rule.name,
      timestamp: Date.now(),
      request: {
        url: details.url,
        method: details.method,
        headers: headers,
        body: requestBody
      },
      responses: {
        old: oldResult,
        new: newResult
      },
      diff: diffResult,
      visualizations: {
        html: generateHtmlVisualization(oldResult, newResult, diffResult, rule),
        summary: generateSummary(oldResult, newResult, diffResult)
      }
    };

    // 保存报告
    await saveReportToStorage(report);

    const totalTime = Date.now() - startTime;
    console.log(`✅ 并行对比完成，耗时 ${totalTime}ms:`, report.id);

  } catch (error) {
    console.error('❌ 并行对比失败:', error);

    // 创建错误报告
    const errorReport: DiffReport = {
      id: 'error_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
      ruleId: rule.id,
      ruleName: rule.name,
      timestamp: Date.now(),
      request: {
        url: details.url,
        method: details.method,
        headers: {},
        body: details.requestBody
      },
      responses: {
        old: { status: 0, body: null, responseTime: 0, error: '对比过程出错' },
        new: { status: 0, body: null, responseTime: 0, error: error.message }
      },
      diff: {
        delta: null,
        hasChanges: true,
        changeCount: 1,
        severity: 'critical'
      },
      visualizations: {
        html: `<div class="error-report"><h4>对比失败</h4><p>错误: ${error.message}</p></div>`,
        summary: `对比失败: ${error.message}`
      }
    };

    await saveReportToStorage(errorReport);
  }
}

// URL转换
function transformUrl(originalUrl: string, rule: InterceptRule): string {
  let newUrl = rule.transformation.newUrl;

  // 处理参数映射
  if (rule.transformation.paramMapping) {
    const url = new URL(originalUrl);
    const newUrlObj = new URL(newUrl);

    // 应用参数映射
    for (const [oldParam, newParam] of Object.entries(rule.transformation.paramMapping)) {
      const value = url.searchParams.get(oldParam);
      if (value !== null) {
        newUrlObj.searchParams.set(newParam, value);
        if (!rule.transformation.preserveOriginalParams) {
          url.searchParams.delete(oldParam);
        }
      }
    }

    // 保留原始参数（如果配置了）
    if (rule.transformation.preserveOriginalParams) {
      for (const [key, value] of url.searchParams.entries()) {
        if (!newUrlObj.searchParams.has(key)) {
          newUrlObj.searchParams.set(key, value);
        }
      }
    }

    newUrl = newUrlObj.toString();
  }

  // 添加内部标记，防止死循环
  const finalUrl = new URL(newUrl);
  finalUrl.searchParams.set('__api_migration_internal__', 'true');

  return finalUrl.toString();
}

// 请求头转换
function transformHeaders(originalHeaders: Record<string, string>, rule: InterceptRule): Record<string, string> {
  const headers = { ...originalHeaders };

  // 移除可能导致问题的头部
  delete headers['content-length'];
  delete headers['host'];

  return headers;
}

// 发送HTTP请求
async function makeRequest(url: string, method: string, headers: Record<string, string>, body: any, rule: InterceptRule) {
  const startTime = Date.now();

  try {
    const requestOptions: RequestInit = {
      method: method,
      headers: headers,
      credentials: rule.transformation.includeCookies ? 'include' : 'omit'
    };

    if (body && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      requestOptions.body = typeof body === 'string' ? body : JSON.stringify(body);
    }

    const response = await fetch(url, requestOptions);
    const responseTime = Date.now() - startTime;

    let responseBody;
    const contentType = response.headers.get('content-type') || '';

    if (contentType.includes('application/json')) {
      try {
        responseBody = await response.json();
      } catch {
        responseBody = await response.text();
      }
    } else {
      responseBody = await response.text();
    }

    return {
      status: response.status,
      body: responseBody,
      responseTime: responseTime
    };

  } catch (error: any) {
    const responseTime = Date.now() - startTime;
    throw {
      status: 0,
      body: null,
      responseTime: responseTime,
      error: error?.message || '请求失败'
    };
  }
}

// 生成差异对比
function generateDiff(oldData: any, newData: any, rule: InterceptRule) {
  try {
    // 如果配置了忽略字段，先处理数据
    let processedOldData = oldData;
    let processedNewData = newData;

    if (rule.diffConfig?.ignoreFields && Array.isArray(rule.diffConfig.ignoreFields)) {
      processedOldData = removeIgnoredFields(oldData, rule.diffConfig.ignoreFields);
      processedNewData = removeIgnoredFields(newData, rule.diffConfig.ignoreFields);
    }

    // 使用jsondiffpatch生成差异
    const delta = differ.diff(processedOldData, processedNewData);

    // 计算变更统计
    const changeCount = countChanges(delta);
    const hasChanges = changeCount > 0;
    const severity = calculateSeverity(changeCount, oldData, newData);

    return {
      delta: delta,
      hasChanges: hasChanges,
      changeCount: changeCount,
      severity: severity
    };

  } catch (error) {
    console.error('生成差异对比失败:', error);
    return {
      delta: null,
      hasChanges: true,
      changeCount: 1,
      severity: 'critical' as const
    };
  }
}

// 移除忽略的字段
function removeIgnoredFields(data: any, ignoreFields: string[]): any {
  if (!data || typeof data !== 'object') {
    return data;
  }

  if (Array.isArray(data)) {
    return data.map(item => removeIgnoredFields(item, ignoreFields));
  }

  const result = { ...data };
  for (const field of ignoreFields) {
    if (field.includes('.')) {
      // 处理嵌套字段，如 'user.id'
      const parts = field.split('.');
      let current = result;
      for (let i = 0; i < parts.length - 1; i++) {
        if (current && typeof current === 'object' && current[parts[i]]) {
          current = current[parts[i]];
        } else {
          break;
        }
      }
      if (current && typeof current === 'object') {
        delete current[parts[parts.length - 1]];
      }
    } else {
      delete result[field];
    }
  }

  return result;
}

// 计算变更数量
function countChanges(delta: any): number {
  if (!delta) return 0;

  let count = 0;

  function traverse(obj: any) {
    if (obj && typeof obj === 'object') {
      for (const key in obj) {
        if (Array.isArray(obj[key]) && obj[key].length === 2) {
          // 这是一个变更 [oldValue, newValue]
          count++;
        } else if (Array.isArray(obj[key]) && obj[key].length === 1) {
          // 这是一个删除 [oldValue]
          count++;
        } else if (Array.isArray(obj[key]) && obj[key].length === 3 && obj[key][2] === 0) {
          // 这是一个添加 [newValue, 0, 0]
          count++;
        } else {
          traverse(obj[key]);
        }
      }
    }
  }

  traverse(delta);
  return count;
}

// 计算严重程度
function calculateSeverity(changeCount: number, oldData: any, newData: any): 'none' | 'minor' | 'major' | 'critical' {
  if (changeCount === 0) return 'none';

  // 检查是否有错误状态
  if ((oldData && oldData.error) || (newData && newData.error)) {
    return 'critical';
  }

  if (changeCount <= 2) return 'minor';
  if (changeCount <= 10) return 'major';
  return 'critical';
}

// 生成HTML可视化
function generateHtmlVisualization(oldResult: any, newResult: any, diffResult: any, rule: InterceptRule): string {
  const severityClass = diffResult.severity;
  const severityMap: Record<string, string> = {
    'none': '无差异',
    'minor': '轻微差异',
    'major': '重要差异',
    'critical': '严重差异'
  };
  const severityText = severityMap[diffResult.severity] || '未知';

  return `
    <div class="api-comparison-report ${severityClass}">
      <div class="report-header">
        <h3>API对比报告</h3>
        <div class="rule-info">
          <span class="rule-name">${rule.name}</span>
          <span class="severity ${severityClass}">${severityText}</span>
        </div>
      </div>

      <div class="response-comparison">
        <div class="response-section">
          <h4>原始API响应</h4>
          <div class="status">状态码: ${oldResult.status}</div>
          <div class="response-time">响应时间: ${oldResult.responseTime}ms</div>
          ${oldResult.error ? `<div class="error">错误: ${oldResult.error}</div>` : ''}
          <pre class="response-body">${JSON.stringify(oldResult.body, null, 2)}</pre>
        </div>

        <div class="response-section">
          <h4>新API响应</h4>
          <div class="status">状态码: ${newResult.status}</div>
          <div class="response-time">响应时间: ${newResult.responseTime}ms</div>
          ${newResult.error ? `<div class="error">错误: ${newResult.error}</div>` : ''}
          <pre class="response-body">${JSON.stringify(newResult.body, null, 2)}</pre>
        </div>
      </div>

      <div class="diff-section">
        <h4>差异详情</h4>
        <div class="diff-stats">
          <span>变更数量: ${diffResult.changeCount}</span>
          <span>严重程度: ${severityText}</span>
        </div>
        ${diffResult.delta ? `<pre class="diff-delta">${JSON.stringify(diffResult.delta, null, 2)}</pre>` : '<p>无差异</p>'}
      </div>
    </div>
  `;
}

// 生成摘要
function generateSummary(oldResult: any, newResult: any, diffResult: any): string {
  if (diffResult.changeCount === 0) {
    return `✅ API响应一致 (${oldResult.responseTime}ms vs ${newResult.responseTime}ms)`;
  }

  if (oldResult.error || newResult.error) {
    return `❌ API请求失败: ${oldResult.error || newResult.error}`;
  }

  const timeDiff = Math.abs(oldResult.responseTime - newResult.responseTime);
  return `⚠️ 发现 ${diffResult.changeCount} 处差异，响应时间差异 ${timeDiff}ms`;
}
